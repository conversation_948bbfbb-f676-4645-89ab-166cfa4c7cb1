<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.OrderMapper">

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into orders(number,status,user_id,address_book_id,
                           order_time,pay_method,pay_status,amount,remark,
                           phone,address,consignee,estimated_delivery_time,
                           delivery_status,pack_amount,tableware_number,
                           tableware_status)
            values(#{number},#{status},#{userId},#{addressBookId},
                   #{orderTime},#{payMethod},#{payStatus},#{amount},#{remark},
                   #{phone},#{address},#{consignee},#{estimatedDeliveryTime},
                   #{deliveryStatus},#{packAmount},#{tablewareNumber},
                   #{tablewareStatus})
    </insert>
    <update id="update" parameterType="com.sky.entity.Orders">
        update orders
        <set>
            <if test="status != null"> status=#{status}, </if>
            <if test="addressBookId != null"> address_book_id=#{addressBookId}, </if>
            <if test="orderTime != null"> order_time=#{orderTime},</if>
            <if test="checkoutTime != null"> checkout_time=#{checkoutTime}, </if>
            <if test="payMethod != null"> pay_method=#{payMethod}, </if>
            <if test="payStatus != null"> pay_status=#{payStatus}, </if>
            <if test="amount != null"> amount=#{amount}, </if>
            <if test="remark != null"> remark=#{remark}, </if>
            <if test="phone != null"> phone=#{phone}, </if>
            <if test="address != null"> address=#{address}, </if>
            <if test="userName != null"> user_name=#{userName}, </if>
            <if test="consignee != null"> consignee=#{consignee} ,</if>
            <if test="cancelReason != null"> cancel_reason=#{cancelReason}, </if>
            <if test="rejectionReason != null"> rejection_reason=#{rejectionReason}, </if>
            <if test="cancelTime != null"> cancel_time=#{cancelTime}, </if>
            <if test="estimatedDeliveryTime != null"> estimated_delivery_time=#{estimatedDeliveryTime}, </if>
            <if test="deliveryStatus != null"> delivery_status=#{deliveryStatus}, </if>
            <if test="deliveryTime != null"> delivery_Time=#{deliveryTime}, </if>
            <if test="packAmount != null"> pack_amount=#{packAmount},</if>
            <if test="tablewareNumber != null"> tableware_number=#{tablewareNumber}, </if>
            <if test="tablewareStatus != null"> tableware_status=#{tablewareStatus}, </if>
        </set>
        <where>
            <if test="id != null"> and id = #{id} </if>
            <if test="number != null and number != ''"> and number = #{number} </if>
        </where>
    </update>

    <select id="pageQuery" resultType="com.sky.entity.Orders">
        select * from orders
        <where>
            <if test="userId != null">and user_id = #{userId}</if>
            <if test="status != null">and status = #{status}</if>
            <if test="number != null and number != ''">and number like concat('%',#{number},'%')</if>
            <if test="phone != null and phone != ''">and phone like concat('%',#{phone},'%')</if>
            <if test="beginTime != null and endTime != null">and order_time between #{beginTime} and #{endTime}</if>
        </where>
        order by order_time desc
    </select>

    <select id="getTurnoverStatisticsOfDay" resultType="java.util.Map" parameterType="java.util.Map">
        select DATE(order_time) as date, sum(amount) as turnover
        from orders
        <where>
            <if test="begin != null">and order_time &gt;= #{map.begin}</if>
            <if test="end != null">and order_time &lt;= #{map.end}</if>
            <if test="status != null">and status = #{map.status}</if>
        </where>
        group by date
        order by date
    </select>
</mapper>