<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.UserMapper">

    <!--除了id都插入-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into user (openid, name, phone, sex, id_number, avatar, create_time)
        values (#{openid}, #{name}, #{phone}, #{sex}, #{idNumber}, #{avatar}, #{createTime})
    </insert>

    <select id="getUserStatisticsOfDay" resultType="java.util.Map" parameterType="java.util.Map">
        select DATE_FORMAT(create_time, '%Y-%m-%d') as date, count(id) as total_user
        from user
        <where>
            <if test="begin != null">and create_time &gt;= #{begin}</if>
            <if test="end != null">and create_time &lt;= #{end}</if>
        </where>
        group by date
        order by date
    </select>
</mapper>