package com.sky.mapper;

import com.github.pagehelper.Page;
import com.sky.dto.GoodsSalesDTO;
import com.sky.dto.OrdersPageQueryDTO;
import com.sky.entity.Orders;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 订单Mapper
 */
@Mapper
public interface OrderMapper {


    /**
     * 插入订单数据
     * @param orders
     */
    void insert(Orders orders);

    /**
     * 根据订单号查询订单
     * @param orderNumber
     */
    @Select("select * from orders where number = #{orderNumber}")
    Orders getByNumber(String orderNumber);

    /**
     * 修改订单信息
     * @param orders
     */
    void update(Orders orders);

    /**
     * 历史订单查询(分页)
     * @param ordersPageQueryDTO
     * @return
     */
    Page<Orders> pageQuery(OrdersPageQueryDTO ordersPageQueryDTO);

    /**
     * 根据id查询订单
     * @param id
     * @return
     */
    @Select("select * from orders where id = #{id}")
    Orders getById(Long id);

    /**
     * 根据状态统计订单数量
     * @param status
     */
    @Select("select count(id) from orders where status = #{status}")
    Integer countStatus(Integer status);

    /**
     * 根据状态和下单时间查询订单
     *
     * @param status
     * @param time
     * @return
     */
    @Select("select * from orders where status = #{status} and order_time < #{time}")
    List<Orders> getByStatusAndOrderTimeLT(@Param("status") Integer status, @Param("time") LocalDateTime time);


    /**
     * 营业额统计
     *
     * @param map
     * @return
     */
    @MapKey("date")
    Map<String,Map<String, Object>> getTurnoverStatisticsOfDay(Map<String, Object> map);

    /**
     * 用户数量统计
     *
     * @param map
     * @return
     */
    @MapKey("date")
    Map<String, Map<String, Object>> getUserStatisticsOfDay(Map<String, Object> map);

    /**
     * 获取指定时间之前的总用户数量
     *
     * @param beginTime
     * @return
     */
    @Select("select count(id) from user where create_time < #{beginTime}")
    Integer getTotalUser(LocalDateTime beginTime);

    /**
     * 订单统计
     *
     * @param map
     * @return
     */
    @MapKey("date")
    Map<String, Map<String, Object>> getOrdersStatisticsOfDay(Map<String, Object> map);

    /**
     * 销量top10
     *
     * @return
     */
    List<GoodsSalesDTO> getSalesTop10(Map<String,Object> map);
}
