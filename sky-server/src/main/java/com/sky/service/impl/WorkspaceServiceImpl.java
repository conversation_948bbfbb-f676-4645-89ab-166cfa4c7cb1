package com.sky.service.impl;

import com.sky.constant.StatusConstant;
import com.sky.entity.Orders;
import com.sky.mapper.DishMapper;
import com.sky.mapper.OrderMapper;
import com.sky.mapper.SetmealMapper;
import com.sky.mapper.UserMapper;
import com.sky.service.WorkspaceService;
import com.sky.vo.BusinessDataVO;
import com.sky.vo.DishOverViewVO;
import com.sky.vo.OrderOverViewVO;
import com.sky.vo.SetmealOverViewVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 工作台服务
 * - 营业额：已完成订单的总金额
 * - 有效订单：已完成订单的数量
 * - 订单完成率：有效订单数 / 总订单数 * 100%
 * - 平均客单价：营业额 / 有效订单数
 * - 新增用户：新增用户的数量
 */
@Service
public class WorkspaceServiceImpl implements WorkspaceService {

    @Autowired
    private UserMapper userMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private DishMapper dishMapper;
    @Autowired
    private SetmealMapper setmealMapper;

    /**
     * 根据时间段统计营业数据
     * @param begin
     * @param end
     * @return
     */
    public BusinessDataVO getBusinessData(LocalDateTime begin, LocalDateTime end) {

        //准备今天的日期
        String today = begin.toLocalDate().toString();

        //准备新增用户数 数据
        /*
            select DATE_FORMAT(create_time, '%Y-%m-%d') as date, count(id) as total_user
            from user
            <where>
                <if test="begin != null">and create_time &gt;= #{begin}</if>
                <if test="end != null">and create_time &lt;= #{end}</if>
            </where>
            group by date
            order by date
         */

        HashMap<String, Object> map = new HashMap<>();
        map.put("begin", begin);
        map.put("end", end);

        Map<String, Map<String, Object>> userStatisticsOfDay = userMapper.getUserStatisticsOfDay(map);

        Integer newUsers = 0;
        if (userStatisticsOfDay != null && !userStatisticsOfDay.isEmpty()) {
            //直接把total_user取出来
            newUsers = (Integer) userStatisticsOfDay.get(today).get("total_user");
        }

        //获取今日总订单数
        Map<String, Map<String, Object>> ordersStatisticsOfDay = orderMapper.getOrdersStatisticsOfDay(map);
        Integer totalOrderCount = 0;
        if (ordersStatisticsOfDay != null && !ordersStatisticsOfDay.isEmpty()) {
            //直接把order_count取出来
            totalOrderCount = (Integer) ordersStatisticsOfDay.get(today).get("order_count");
        }

        //准备有效订单数 数据
        /*
            select DATE_FORMAT(order_time, '%Y-%m-%d') as date, count(id) as order_count
            from orders
            <where>
                <if test="begin != null">and order_time &gt;= #{begin}</if>
                <if test="end != null">and order_time &lt;= #{end}</if>
                <if test="status != null">and status = #{status}</if>
            </where>
            group by date
            order by date
         */
        map.put("status", Orders.COMPLETED);
        Map<String, Map<String, Object>> validOrderStatisticsOfDay = orderMapper.getOrdersStatisticsOfDay(map);
        Integer validOrderCount = 0;
        if (validOrderStatisticsOfDay != null && !validOrderStatisticsOfDay.isEmpty()) {
            //直接把order_count取出来
            validOrderCount = (Integer) validOrderStatisticsOfDay.get(today).get("order_count");
        }

        //准备营业额 数据
        /*
            select DATE_FORMAT(order_time, '%Y-%m-%d') as date, sum(amount) as turnover
            from orders
            <where>
                <if test="begin != null">and order_time &gt;= #{begin}</if>
                <if test="end != null">and order_time &lt;= #{end}</if>
                <if test="status != null">and status = #{status}</if>
            </where>
            group by date
            order by date
         */
        Map<String, Map<String, Object>> turnoverStatisticsOfDay = orderMapper.getTurnoverStatisticsOfDay(map);
        Double turnover = 0.0;
        if (turnoverStatisticsOfDay != null && !turnoverStatisticsOfDay.isEmpty()) {
            //直接把turnover取出来
            turnover = (Double) turnoverStatisticsOfDay.get(today).get("turnover");
        }

        //计算订单完成率 数据
        Double orderCompletionRate = 0.0;
        if (totalOrderCount != 0) {
            orderCompletionRate = (double) (validOrderCount / totalOrderCount);
        }

        //计算平均客单价 数据
        Double unitPrice = 0.0;
        if (validOrderCount != 0) {
            unitPrice = turnover / validOrderCount;
        }


        //封装数据
        return BusinessDataVO.builder()
                .turnover(turnover)
                .validOrderCount(validOrderCount)
                .orderCompletionRate(orderCompletionRate)
                .unitPrice(unitPrice)
                .newUsers(newUsers)
                .build();
    }

    /**
     * 查询订单管理数据
     * @return
     */
    public OrderOverViewVO getOrderOverView() {

        //准备今日时间
        String today = LocalDateTime.now().toLocalDate().toString();

        //查询各个状态的订单数量

        //准备今日全部订单数量 数据

        HashMap<String, Object> map = new HashMap<>();
        map.put("begin",LocalDateTime.now().with(LocalTime.MIN));

        Map<String, Map<String, Object>> ordersStatisticsOfDay = orderMapper.getOrdersStatisticsOfDay(map);
        Integer allOrders =
                ordersStatisticsOfDay != null && !ordersStatisticsOfDay.isEmpty()
                        ? (Integer) ordersStatisticsOfDay.get(today).get("order_count") : 0;

        //准备今日已取消订单数量 数据
        map.put("status", Orders.CANCELLED);
        Map<String, Map<String, Object>> cancelledOrderStatisticsOfDay = orderMapper.getOrdersStatisticsOfDay(map);
        Integer cancelledOrders =
                cancelledOrderStatisticsOfDay != null && !cancelledOrderStatisticsOfDay.isEmpty()
                        ? (Integer) cancelledOrderStatisticsOfDay.get(today).get("order_count") : 0;

        //准备今日已完成订单数量 数据
        map.put("status", Orders.COMPLETED);
        Map<String, Map<String, Object>> completedOrderStatisticsOfDay = orderMapper.getOrdersStatisticsOfDay(map);
        Integer completedOrders =
                completedOrderStatisticsOfDay != null && !completedOrderStatisticsOfDay.isEmpty()
                        ? (Integer) completedOrderStatisticsOfDay.get(today).get("order_count") : 0;

        //准备今日待接单订单数量 数据
        map.put("status", Orders.TO_BE_CONFIRMED);
        Map<String, Map<String, Object>> toBeConfirmedOrderStatisticsOfDay = orderMapper.getOrdersStatisticsOfDay(map);
        Integer waitingOrders =
                toBeConfirmedOrderStatisticsOfDay != null && !toBeConfirmedOrderStatisticsOfDay.isEmpty()
                        ? (Integer) toBeConfirmedOrderStatisticsOfDay.get(today).get("order_count") : 0;

        //准备今日待派送订单数量 数据
        map.put("status", Orders.CONFIRMED);
        Map<String, Map<String, Object>> confirmedOrderStatisticsOfDay = orderMapper.getOrdersStatisticsOfDay(map);
        Integer deliveredOrders =
                confirmedOrderStatisticsOfDay != null && !confirmedOrderStatisticsOfDay.isEmpty()
                        ? (Integer) confirmedOrderStatisticsOfDay.get(today).get("order_count") : 0;

        //封装数据
        return OrderOverViewVO.builder()
                .waitingOrders(waitingOrders)
                .deliveredOrders(deliveredOrders)
                .completedOrders(completedOrders)
                .cancelledOrders(cancelledOrders)
                .allOrders(allOrders)
                .build();
    }

    /**
     * 查询菜品总览
     * @return
     */
    public DishOverViewVO getDishOverView() {

        //准备已停售菜品数量 数据
        Long discontinued = dishMapper.countByStatusAndIds(StatusConstant.DISABLE, null);
        //准备已启售菜品数据 数据
        Long sold = dishMapper.countByStatusAndIds(StatusConstant.ENABLE, null);

        //封装数据
        return DishOverViewVO.builder()
                .sold(sold.intValue())
                .discontinued(discontinued.intValue())
                .build();
    }

    /**
     * 查询套餐总览
     * @return
     */
    public SetmealOverViewVO getSetmealOverView() {

        //准备已停售套餐数量 数据
        Long discontinued = setmealMapper.countByStatusAndIds(StatusConstant.DISABLE, null);
        //准备已启售套餐数据 数据
        Long sold = setmealMapper.countByStatusAndIds(StatusConstant.ENABLE, null);

        //封装数据
        return SetmealOverViewVO.builder()
                .sold(sold.intValue())
                .discontinued(discontinued.intValue())
                .build();
    }
}
