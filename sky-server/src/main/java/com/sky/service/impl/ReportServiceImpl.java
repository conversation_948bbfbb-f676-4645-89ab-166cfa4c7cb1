package com.sky.service.impl;

import com.sky.dto.GoodsSalesDTO;
import com.sky.entity.Orders;
import com.sky.mapper.OrderMapper;
import com.sky.mapper.UserMapper;
import com.sky.service.ReportService;
import com.sky.vo.OrderReportVO;
import com.sky.vo.SalesTop10ReportVO;
import com.sky.vo.TurnoverReportVO;
import com.sky.vo.UserReportVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ReportServiceImpl implements ReportService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private UserMapper userMapper;


    /**
     * 营业额统计
     * @param begin
     * @param end
     * @return
     */
    public TurnoverReportVO getTurnoverStatistics(LocalDate begin, LocalDate end) {
        //准备日期数据
        List<LocalDate> dateList = getDateList(begin, end);

        //准备营业额数据
        /*
        select DATE_FORMAT(order_time, '%Y-%m-%d') as date, sum(amount) as turnover
        from orders
        where order_time >= ? and order_time <= ? and status = ?
        group by date
        order by date
         */
        Map<String, Object> map = new HashMap();
        LocalDateTime beginTime = LocalDateTime.of(begin, LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(end, LocalTime.MAX);
        map.put("begin", beginTime);
        map.put("end", endTime);
        map.put("status", Orders.COMPLETED);
        Map<String,Map<String, Object>> turnoverMap = orderMapper.getTurnoverStatisticsOfDay(map);
        //若数据库查询结果中没有改日期的营业额数据，则返回0.0
        //用stream流处理
        List<Double> turnoverList = dateList.stream()
                .map(date -> {
                    String dateStr = date.toString();
                    Map<String, Object> dayData = turnoverMap.get(dateStr);
                    return dayData != null ?
                            Double.parseDouble(dayData.get("turnover").toString()) : 0.0;
                })
                .collect(Collectors.toList());


        //封装数据
        return TurnoverReportVO.builder()
                .dateList(StringUtils.join(dateList, ","))
                .turnoverList(StringUtils.join(turnoverList, ","))
                .build();
    }

    /**
     * 用户数量统计
     * @param begin
     * @param end
     * @return
     */
    public UserReportVO getUserStatistics(LocalDate begin, LocalDate end) {
        //准备日期数据
        List<LocalDate> dateList = getDateList(begin, end);

        //准备新增用户数量数据
        /*
            select DATE_FORMAT(create_time, '%Y-%m-%d') as date, count(id) as total_user
            from user
            where create_time >= ? and create_time <= ?
            group by date
            order by date
         */
        Map<String, Object> map = new HashMap<>();
        LocalDateTime beginTime = LocalDateTime.of(begin, LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(end, LocalTime.MAX);
        map.put("begin", beginTime);
        map.put("end", endTime);
        Map<String,Map<String, Object>> userMap = userMapper.getUserStatisticsOfDay(map);

        List<Integer> newUserList = dateList.stream()
                .map(date -> {
                    String dateStr = date.toString();
                    Map<String, Object> dayData = userMap.get(dateStr);
                    return dayData != null ?
                            Integer.parseInt(dayData.get("total_user").toString()) : 0;
                })
                .collect(Collectors.toList());

        //准备总用户数量数据
        //获取begin之前的总用户数量
        /*
            select count(id) from user where create_time < ?
         */
        Integer totalUser = userMapper.getTotalUser(beginTime);
        List<Integer> totalUserList = new ArrayList<>();
        for (Integer user : newUserList) {
            totalUser += user;
            totalUserList.add(totalUser);
        }
        //封装数据
        return UserReportVO.builder()
                .dateList(StringUtils.join(dateList, ","))
                .totalUserList(StringUtils.join(totalUserList, ","))
                .newUserList(StringUtils.join(newUserList, ","))
                .build();
    }

    /**
     * 订单统计
     * @param begin
     * @param end
     * @return
     */
    public OrderReportVO getOrdersStatistics(LocalDate begin, LocalDate end) {
        //准备日期数据
        List<LocalDate> dateList = getDateList(begin, end);
        //准备订单数列表数据
        /*
            select DATE_FORMAT(order_time, '%Y-%m-%d') as date, count(id) as order_count
            from orders
            where order_time >= ? and order_time <= ?
            group by date
            order by date
         */
        Map<String, Object> map = new HashMap<>();
        LocalDateTime beginTime = LocalDateTime.of(begin, LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(end, LocalTime.MAX);
        map.put("begin", beginTime);
        map.put("end", endTime);
        Map<String,Map<String, Object>> orderMap = orderMapper.getOrdersStatisticsOfDay(map);
        List<Integer> orderCountList = new ArrayList<>();

        //准备有效订单数列表数据
        map.put("status", Orders.COMPLETED);
        Map<String,Map<String, Object>> validOrderMap = orderMapper.getOrdersStatisticsOfDay(map);
        List<Integer> validOrderCountList = new ArrayList<>();
        for (LocalDate date : dateList) {
            String dateStr = date.toString();
            if(orderMap.containsKey(dateStr)){
                orderCountList.add(Integer.parseInt(orderMap.get(dateStr).get("order_count").toString()));
            }else{
                orderCountList.add(0);
            }
            if(validOrderMap.containsKey(dateStr)){
                validOrderCountList.add(Integer.parseInt(validOrderMap.get(dateStr).get("order_count").toString()));
            }else{
                validOrderCountList.add(0);
            }
        }
        //获取订单总数
        Integer totalOrderCount = orderCountList.stream().reduce(Integer::sum).get();
        //获取有效订单总数
        Integer validOrderCount = validOrderCountList.stream().reduce(Integer::sum).get();
        //计算订单完成率
        Double orderCompletionRate = 0.0;
        if(totalOrderCount != 0){
            orderCompletionRate = Double.valueOf(validOrderCount) / totalOrderCount;
        }
        //封装数据
        return OrderReportVO.builder()
                .dateList(StringUtils.join(dateList, ","))
                .orderCountList(StringUtils.join(orderCountList, ","))
                .validOrderCountList(StringUtils.join(validOrderCountList, ","))
                .totalOrderCount(totalOrderCount)
                .validOrderCount(validOrderCount)
                .orderCompletionRate(orderCompletionRate)
                .build();
    }

    /**
     * 销量top10
     * @return
     * **业务规则:
     * - 根据时间选择区间，展示销量前10的商品（包括菜品和套餐）
     * - 基于可视化报表的柱状图降序展示商品销量
     * - 此处的销量为商品销售的份数
     */
    public SalesTop10ReportVO getSalesTop10(LocalDate begin, LocalDate end) {
        //准备商品名称列表和销量列表
        /*
            select name, sum(all od.number) sum_number
            from orders o left join order_detail od
            on o.id = od.order_id
            where order_time >= ?
                and order_time <= ?
                and status = 5
            group by name
            order by sum_number desc
            limit 0, 10
         */
        //查询数据
        Map<String, Object> map = new HashMap<>();
        LocalDateTime beginTime = LocalDateTime.of(begin, LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(end, LocalTime.MAX);
        map.put("begin", beginTime);
        map.put("end", endTime);
        List<GoodsSalesDTO> list = orderMapper.getSalesTop10(map);
        //准备商品名称列表和销量列表
        List<String> nameList = new ArrayList<>();
        List<Integer> numberList = new ArrayList<>();
        for (GoodsSalesDTO topMap : list) {
            nameList.add(topMap.getName());
            numberList.add(topMap.getNumber());
        }
        //封装数据
        return SalesTop10ReportVO.builder()
                .nameList(StringUtils.join(nameList, ","))
                .numberList(StringUtils.join(numberList, ","))
                .build();
    }

    /**
     * 获取日期数据的方法
     */
    private List<LocalDate> getDateList(LocalDate begin, LocalDate end) {
        List<LocalDate> dateList = new ArrayList<>();
        dateList.add(begin);
        while(!begin.equals(end)){
            //日期计算，获得指定日期后1天的日期
            begin = begin.plusDays(1);
            dateList.add(begin);
        }
        return dateList;
    }
}
