package com.sky.service.impl;

import com.sky.entity.Orders;
import com.sky.mapper.OrderMapper;
import com.sky.service.ReportService;
import com.sky.vo.TurnoverReportVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ReportServiceImpl implements ReportService {

    @Autowired
    private OrderMapper orderMapper;


    /**
     * 营业额统计
     * @param begin
     * @param end
     * @return
     */
    public TurnoverReportVO getTurnoverStatistics(LocalDate begin, LocalDate end) {
        //准备日期数据
        List<LocalDate> dateList = new ArrayList<>();
        dateList.add(begin);
        LocalDate beginTemp = begin;
        while(!beginTemp.equals(end)){
            //日期计算，获得指定日期后1天的日期
            beginTemp = beginTemp.plusDays(1);
            dateList.add(beginTemp);
        }

        //准备营业额数据
        /*
        select DATE(order_time) as date, sum(amount) as turnover
        from orders
        where order_time >= ? and order_time <= ? and status = ?
        group by date
        order by date
         */
        Map<String, Object> map = new HashMap();
        LocalDateTime beginTime = LocalDateTime.of(begin, LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(end, LocalTime.MAX);
        map.put("begin", beginTime);
        map.put("end", endTime);
        map.put("status", Orders.COMPLETED);
        Map<String,Map<String, Double>> turnoverMap = orderMapper.getTurnoverStatisticsOfDay(map);
        //若数据库查询结果中没有改日期的营业额数据，则返回0.0
        //用stream流处理
        List<Double> turnoverList = dateList.stream()
                .map(date -> {
                    String dateStr = date.toString();
                    Map<String, Double> dayData = turnoverMap.get(dateStr);
                    return dayData != null ?
                            dayData.get("turnover").doubleValue() : 0.0;
                })
                .collect(Collectors.toList());


        //封装数据
        return TurnoverReportVO.builder()
                .dateList(StringUtils.join(dateList, ","))
                .turnoverList(StringUtils.join(turnoverList, ","))
                .build();
    }
}
